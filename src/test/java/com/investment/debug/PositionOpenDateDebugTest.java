package com.investment.debug;

import com.investment.database.DatabaseManager;
import com.investment.service.PositionsService;
import com.investment.model.Position;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Debug test to investigate the Position openDate null issue.
 */
public class PositionOpenDateDebugTest {

    private DatabaseManager databaseManager;
    private PositionsService positionsService;

    @BeforeEach
    public void setup() {
        // Use the actual database for debugging
        databaseManager = new DatabaseManager();
        databaseManager.initDatabase();
        positionsService = new PositionsService(databaseManager, null, null);
    }

    @AfterEach
    public void cleanup() {
        if (databaseManager != null) {
            databaseManager.closeConnection();
        }
    }

    @Test
    public void debugPositionOpenDateIssue() throws SQLException {
        // Write output to a file for easier debugging
        java.io.PrintWriter writer = null;
        try {
            writer = new java.io.PrintWriter("position_debug_output.txt");
            writer.println("=== Position OpenDate Debug Test ===");

        // 1. Check raw database data
        writer.println("\n1. Checking raw database data for positions with null open_date:");
        List<Map<String, Object>> rawPositions = databaseManager.getPositions(null, null, null);

        int nullOpenDateCount = 0;
        for (Map<String, Object> rawPosition : rawPositions) {
            Object openDate = rawPosition.get("open_date");
            if (openDate == null) {
                nullOpenDateCount++;
                writer.printf("Position ID %s has null open_date%n", rawPosition.get("id"));
            }
        }
        writer.printf("Found %d positions with null open_date out of %d total positions%n",
                         nullOpenDateCount, rawPositions.size());

        // 2. Check Position objects after mapping
        writer.println("\n2. Checking Position objects after mapping:");
        List<Position> positions = positionsService.getPositions(null, null, null);

        int nullOpenDateObjectCount = 0;
        for (Position position : positions) {
            if (position.getOpenDate() == null) {
                nullOpenDateObjectCount++;
                writer.printf("Position object ID %s has null openDate%n", position.getId());

                // Get the raw data for this specific position to compare
                Map<String, Object> rawData = databaseManager.getPositionById(position.getId());
                Object rawOpenDate = rawData.get("open_date");
                writer.printf("  Raw open_date from DB: %s (type: %s)%n",
                                 rawOpenDate, rawOpenDate != null ? rawOpenDate.getClass().getSimpleName() : "null");
            }
        }
        writer.printf("Found %d Position objects with null openDate out of %d total positions%n",
                         nullOpenDateObjectCount, positions.size());

        // 3. Test specific position retrieval (if any positions exist)
        if (!positions.isEmpty()) {
            Position firstPosition = positions.get(0);
            writer.printf("\n3. Testing specific position retrieval for ID %s:%n", firstPosition.getId());

            Optional<Position> retrievedPosition = positionsService.getPositionById(firstPosition.getId());
            if (retrievedPosition.isPresent()) {
                Position pos = retrievedPosition.get();
                writer.printf("Retrieved position openDate: %s%n", pos.getOpenDate());
                writer.printf("Retrieved position symbol: %s%n", pos.getSymbol());
                writer.printf("Retrieved position status: %s%n", pos.getStatus());

                // Check raw data again
                Map<String, Object> rawData = databaseManager.getPositionById(pos.getId());
                writer.printf("Raw open_date: %s%n", rawData.get("open_date"));
                writer.printf("Raw symbol: %s%n", rawData.get("symbol"));
                writer.printf("Raw status: %s%n", rawData.get("status"));
            }
        }

        // 4. Show sample of positions with their open dates
        writer.println("\n4. Sample of first 5 positions with their open dates:");
        for (int i = 0; i < Math.min(5, positions.size()); i++) {
            Position pos = positions.get(i);
            writer.printf("Position %s (%s): openDate=%s, symbol=%s%n",
                             pos.getId(), pos.getStatus(), pos.getOpenDate(), pos.getSymbol());
        }

        // 5. Check creation dates and other details for positions with null open_date
        writer.println("\n5. Detailed analysis of positions with null open_date:");
        for (Position pos : positions) {
            if (pos.getOpenDate() == null) {
                Map<String, Object> rawData = databaseManager.getPositionById(pos.getId());
                writer.printf("Position ID %s (%s):%n", pos.getId(), pos.getSymbol());
                writer.printf("  created_date: %s%n", rawData.get("created_date"));
                writer.printf("  updated_date: %s%n", rawData.get("updated_date"));
                writer.printf("  trade_price: %s%n", rawData.get("trade_price"));
                writer.printf("  status: %s%n", rawData.get("status"));
                writer.printf("  open_date: %s%n", rawData.get("open_date"));
                writer.printf("  close_date: %s%n", rawData.get("close_date"));
                writer.println();
            }
        }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }
}
