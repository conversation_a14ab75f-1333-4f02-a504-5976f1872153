package com.investment.debug;

import com.investment.database.DatabaseManager;
import com.investment.service.PositionsService;
import com.investment.service.RiskManagementService;
import com.investment.model.Position;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * Test to verify that RiskManagementService.calculateEnhancedEffectiveStopValue
 * now works correctly after fixing the null openDate issue.
 */
public class RiskManagementServiceValidationTest {

    private DatabaseManager databaseManager;
    private PositionsService positionsService;
    private RiskManagementService riskManagementService;

    @BeforeEach
    public void setup() {
        databaseManager = new DatabaseManager();
        databaseManager.initDatabase();
        positionsService = new PositionsService(databaseManager, null, null);
        riskManagementService = new RiskManagementService();
        // Use reflection to set the databaseManager in RiskManagementService
        try {
            java.lang.reflect.Field field = RiskManagementService.class.getDeclaredField("databaseManager");
            field.setAccessible(true);
            field.set(riskManagementService, databaseManager);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject databaseManager", e);
        }
    }

    @AfterEach
    public void cleanup() {
        if (databaseManager != null) {
            databaseManager.closeConnection();
        }
    }

    @Test
    public void testRiskManagementServiceValidation() throws SQLException {
        System.out.println("=== RiskManagementService Validation Test ===");
        
        // Get all positions
        List<Position> positions = positionsService.getPositions(null, null, null);
        System.out.printf("Found %d positions to test%n", positions.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (Position position : positions) {
            System.out.printf("\nTesting position ID %s (%s):%n", position.getId(), position.getSymbol());
            System.out.printf("  openDate: %s%n", position.getOpenDate());
            System.out.printf("  symbol: %s%n", position.getSymbol());
            System.out.printf("  status: %s%n", position.getStatus());
            
            try {
                // Test the RiskManagementService validation
                BigDecimal result = riskManagementService.calculateEnhancedEffectiveStopValue(position);
                
                if (result != null) {
                    System.out.printf("  ✓ SUCCESS: Enhanced effective stop value = %s%n", result);
                    successCount++;
                } else {
                    System.out.printf("  ⚠ WARNING: Enhanced effective stop value returned null (may be due to insufficient data)%n");
                    successCount++; // Still counts as success since validation passed
                }
                
            } catch (Exception e) {
                System.out.printf("  ✗ FAILURE: %s%n", e.getMessage());
                failureCount++;
                e.printStackTrace();
            }
        }
        
        System.out.printf("\n=== Test Results ===%n");
        System.out.printf("Total positions tested: %d%n", positions.size());
        System.out.printf("Successful validations: %d%n", successCount);
        System.out.printf("Failed validations: %d%n", failureCount);
        
        if (failureCount == 0) {
            System.out.println("🎉 ALL TESTS PASSED! The openDate fix resolved the validation issue.");
        } else {
            System.out.println("❌ Some tests failed. There may be other issues to investigate.");
        }
    }
    
    @Test
    public void testSpecificPositionValidation() throws SQLException {
        System.out.println("\n=== Testing Specific Position (NVDA) ===");
        
        // Find the NVDA position that was mentioned in the original issue
        List<Position> positions = positionsService.getPositions("NVDA", null, null);
        
        if (positions.isEmpty()) {
            System.out.println("No NVDA position found");
            return;
        }
        
        Position nvdaPosition = positions.get(0);
        System.out.printf("NVDA Position ID %s:%n", nvdaPosition.getId());
        System.out.printf("  openDate: %s%n", nvdaPosition.getOpenDate());
        System.out.printf("  symbol: %s%n", nvdaPosition.getSymbol());
        System.out.printf("  status: %s%n", nvdaPosition.getStatus());
        
        // Test the validation that was originally failing
        if (nvdaPosition.getOpenDate() == null || nvdaPosition.getSymbol() == null || nvdaPosition.getOpenDate() == null) {
            System.out.println("❌ VALIDATION FAILED: Required fields are null");
            return;
        }
        
        System.out.println("✓ VALIDATION PASSED: All required fields are present");
        
        // Test the full method
        try {
            BigDecimal result = riskManagementService.calculateEnhancedEffectiveStopValue(nvdaPosition);
            System.out.printf("✓ Enhanced effective stop value calculation completed: %s%n", result);
        } catch (Exception e) {
            System.out.printf("❌ Enhanced effective stop value calculation failed: %s%n", e.getMessage());
            e.printStackTrace();
        }
    }
}
