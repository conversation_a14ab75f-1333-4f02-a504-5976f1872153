package com.investment.debug;

import com.investment.database.DatabaseManager;
import com.investment.database.PositionOpenDateFix;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

/**
 * Test to run the position open_date fix.
 */
public class RunPositionOpenDateFix {

    private DatabaseManager databaseManager;

    @BeforeEach
    public void setup() {
        databaseManager = new DatabaseManager();
        databaseManager.initDatabase();
    }

    @AfterEach
    public void cleanup() {
        if (databaseManager != null) {
            databaseManager.closeConnection();
        }
    }

    @Test
    public void runPositionOpenDateFix() throws Exception {
        System.out.println("=== Running Position OpenDate Fix ===");
        
        PositionOpenDateFix fix = new PositionOpenDateFix(databaseManager);
        
        // Run the fix
        int updatedCount = fix.fixNullOpenDates();
        System.out.printf("Updated %d positions with null open_date%n", updatedCount);
        
        // Validate the fix
        boolean allValid = fix.validateAllPositionsHaveOpenDate();
        System.out.printf("Validation result: %s%n", allValid ? "PASSED" : "FAILED");
        
        System.out.println("=== Fix Complete ===");
    }
}
